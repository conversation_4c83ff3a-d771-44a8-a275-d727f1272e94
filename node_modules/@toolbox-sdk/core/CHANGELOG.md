# Changelog

## 0.1.0 (2025-06-23)


### Features

* **toolbox-core:** Add load toolset method ([#17](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/17)) ([2449b18](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/2449b186778090bf0e3a352a08f961de584208bc))
* **toolbox-core:** Added basic tool and client with loadTool() ([#12](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/12)) ([cc6072b](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/cc6072bf7f5e4d8a74c87b7e3900ec6f6e3179db))
* **toolbox-core:** Added toolbox protocol ([#7](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/7)) ([35822b2](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/35822b22ea423e7c1a514f1ab8240b320bf0f14f))
* **toolbox-core:** allow users to import via the require syntax ([#55](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/55)) ([41144e5](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/41144e5697b17f452ee5b8efd01bc4bbecca1b91))
* **toolbox-core:** cache id tokens for client headers ([#60](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/60)) ([952013a](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/952013a43e5e6afae262cc3194f906383b475c7d))
* **toolbox-core:** Add auth token getters ([#38](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/38)) ([6611291](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/661129160801f7f89de4fe7920017b4b23524ab7))
* **toolbox-core:** add bound params ([#25](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/25)) ([5238fca](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/5238fca1321a13aaf20b1958fbf4422d6d563968))
* **toolbox-core:** add client headers ([#23](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/23)) ([edb347c](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/edb347c7256dbd4434ad4e8b52ba71c53351b80a))
* **toolbox-core:** Add helper methods for retrieving Google ID Tokens ([#41](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/41)) ([794f40a](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/794f40a98e59d902b2593e2f26182aaf72c88923))


### Documentation

* **toolbox-core:** added a developer.md file ([#19](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/19)) ([c8cc15f](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/c8cc15f20bcfe1962c5301c9952deaa385ecab16))
* **toolbox-core:** Add guide on how to use with various orchestration frameworks ([#46](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/46)) ([58da3b1](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/58da3b1e25cc0f029105f8c3daf42347ec34d139))
* **toolbox-core:** add README ([#35](https://github.com/googleapis/mcp-toolbox-sdk-js/issues/35)) ([68105c1](https://github.com/googleapis/mcp-toolbox-sdk-js/commit/68105c1e98298efc6290e4a1ae6d9a792850150a))
