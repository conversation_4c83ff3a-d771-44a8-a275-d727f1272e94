{"version": 3, "file": "tool.js", "sourceRoot": "", "sources": ["../../src/toolbox_core/tool.ts"], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,kDAAkD;AAClD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;AAEjC,OAAO,EAAY,QAAQ,EAAc,MAAM,KAAK,CAAC;AAGrD,OAAO,EAAC,WAAW,EAAC,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAGL,wBAAwB,EACxB,YAAY,GACb,MAAM,YAAY,CAAC;AAOpB;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,aAAqB;IAC9C,OAAO,GAAG,aAAa,QAAQ,CAAC;AAClC,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,SAAS,WAAW,CAClB,OAAsB,EACtB,OAAe,EACf,IAAY,EACZ,WAAmB,EACnB,WAAmC,EACnC,mBAAqC,EAAE,EACvC,sBAA2C,EAAE,EAC7C,sBAAgC,EAAE,EAClC,cAA2B,EAAE,EAC7B,gBAAqC,EAAE;IAEvC,IACE,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC;QACvC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAC/B,CAAC;QACD,OAAO,CAAC,IAAI,CACV,2FAA2F,CAC5F,CAAC;IACJ,CAAC;IAED,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACtD,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAC5E,MAAM,UAAU,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9E,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CACb,sBAAsB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kGAAkG,CAC9I,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,OAAO,aAAa,IAAI,SAAS,CAAC;IACrD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC3C,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,CACtC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC;IAEF,MAAM,QAAQ,GAAG,KAAK,WACpB,gBAAyC,EAAE;QAE3C,IACE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC;YAC3C,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAC9B,CAAC;YACD,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CACpD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC9C,CAAC;YACF,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CACb,iFAAiF;gBAC/E,GAAG,eAAe;aACnB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACd,CAAC;QACJ,CAAC;QAED,IAAI,iBAA0C,CAAC;QAC/C,IAAI,CAAC;YACH,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;gBAC9B,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CACpC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC,OAAO,EAAE,CACtD,CAAC;gBACF,MAAM,IAAI,KAAK,CACb,wCAAwC,IAAI,UAAU,aAAa,CAAC,IAAI,CACtE,OAAO,CACR,EAAE,CACJ,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,+BAA+B,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CACvC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC;YAC3C,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QACF,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAEhE,MAAM,OAAO,GAAG,EAAC,GAAG,iBAAiB,EAAE,GAAG,mBAAmB,EAAC,CAAC;QAE/D,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,KAAK,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;YACtE,MAAM,mBAAmB,GAAG,MAAM,YAAY,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CACb,kBAAkB,UAAU,gCAAgC,CAC7D,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,UAAU,CAAC,GAAG,mBAAmB,CAAC;QAC5C,CAAC;QACD,KAAK,MAAM,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC1E,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,WAAW,CAAC,CAAC;YAC9C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CACb,0BAA0B,WAAW,4BAA4B,CAClE,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC;QAClD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAkB,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE;gBACnE,OAAO;aACR,CAAC,CAAC;YACH,OAAO,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ;gBACtC,CAAC,CAAC,QAAQ,CAAC,IAAI;gBACf,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,WAAW,CAAC,yBAAyB,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC,CAAC;IACF,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;IAC9B,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;IACnC,QAAQ,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC7C,QAAQ,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACnD,QAAQ,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;IACnD,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC;IAEvC,QAAQ,CAAC,OAAO,GAAG;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC,CAAC;IACF,QAAQ,CAAC,cAAc,GAAG;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC;IACF,QAAQ,CAAC,cAAc,GAAG;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC,CAAC;IAEF,QAAQ,CAAC,mBAAmB,GAAG,UAC7B,mBAAqC;QAErC,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5D,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC1D,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC7C,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC7B,CAAC;QACF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CACb,8BAA8B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mCAAmC,IAAI,CAAC,QAAQ,KAAK,CACzG,CAAC;QACJ,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/D,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACrD,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAC3B,CAAC;QACF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CACb,sBAAsB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,kGAAkG,CACpJ,CAAC;QACJ,CAAC;QAED,MAAM,eAAe,GAAG,EAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,mBAAmB,EAAC,CAAC;QAE3E,MAAM,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,CAAC,GACxD,wBAAwB,CACtB,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,mBAAmB,EACxB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CACjC,CAAC;QAEJ,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CACb,8BAA8B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,QAAQ,KAAK,CAC7F,CAAC;QACJ,CAAC;QAED,OAAO,WAAW,CAChB,OAAO,EACP,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,eAAe,EACf,iBAAiB,EACjB,iBAAiB,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC,CAAC;IAEF,QAAQ,CAAC,kBAAkB,GAAG,UAC5B,UAAkB,EAClB,UAA2B;QAE3B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAC,CAAC,UAAU,CAAC,EAAE,UAAU,EAAC,CAAC,CAAC;IAC9D,CAAC,CAAC;IAEF,QAAQ,CAAC,UAAU,GAAG,UAAU,YAAyB;QACvD,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACzD,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAClD,IAAI,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CACb,wCAAwC,SAAS,+BAA+B,IAAI,CAAC,QAAQ,IAAI,CAClG,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3C,MAAM,IAAI,KAAK,CACb,iDAAiD,SAAS,cAAc,IAAI,CAAC,QAAQ,IAAI,CAC1F,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,EAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,YAAY,EAAC,CAAC;QAC9D,OAAO,WAAW,CAChB,OAAO,EACP,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,mBAAmB,EACxB,cAAc,EACd,IAAI,CAAC,aAAa,CACnB,CAAC;IACJ,CAAC,CAAC;IAEF,QAAQ,CAAC,SAAS,GAAG,UAAU,SAAiB,EAAE,UAAsB;QACtE,OAAO,IAAI,CAAC,UAAU,CAAC,EAAC,CAAC,SAAS,CAAC,EAAE,UAAU,EAAC,CAAC,CAAC;IACpD,CAAC,CAAC;IACF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,OAAO,EAAC,WAAW,EAAC,CAAC"}