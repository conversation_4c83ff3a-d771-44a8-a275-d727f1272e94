import { ToolboxTool } from './tool.js';
import { type AxiosInstance } from 'axios';
import { BoundParams } from './utils.js';
import { AuthTokenGetters } from './tool.js';
export type HeaderFunction = () => string | Promise<string>;
export type ClientHeaderProvider = string | HeaderFunction;
export type ClientHeadersConfig = Record<string, ClientHeaderProvider>;
/**
 * An asynchronous client for interacting with a Toolbox service.
 */
declare class ToolboxClient {
    #private;
    /**
     * Initializes the ToolboxClient.
     * @param {string} url - The base URL for the Toolbox service API (e.g., "http://localhost:5000").
     * @param {AxiosInstance} [session] - Optional Axios instance for making HTTP
     * requests. If not provided, a new one will be created.
     * @param {ClientHeadersConfig} [clientHeaders] - Optional initial headers to
     * be included in each request.
     */
    constructor(url: string, session?: AxiosInstance | null, clientHeaders?: ClientHeadersConfig | null);
    /**
     * Asynchronously loads a tool from the server.
     * Retrieves the schema for the specified tool from the Toolbox server and
     * returns a callable (`ToolboxTool`) that can be used to invoke the
     * tool remotely.
     *
     * @param {string} name - The unique name or identifier of the tool to load.
     * @param {AuthTokenGetters | null} [authTokenGetters] - Optional map of auth service names to token getters.
     * @param {BoundParams | null} [boundParams] - Optional parameters to pre-bind to the tool.
     * @returns {Promise<ReturnType<typeof ToolboxTool>>} A promise that resolves
     * to a ToolboxTool function, ready for execution.
     * @throws {Error} If the tool is not found in the manifest, the manifest structure is invalid,
     * or if there's an error fetching data from the API.
     */
    loadTool(name: string, authTokenGetters?: AuthTokenGetters | null, boundParams?: BoundParams | null): Promise<ReturnType<typeof ToolboxTool>>;
    /**
     * Asynchronously fetches a toolset and loads all tools defined within it.
     *
     * @param {string | null} [name] - Name of the toolset to load. If null or undefined, loads the default toolset.
     * @param {AuthTokenGetters | null} [authTokenGetters] - Optional map of auth service names to token getters.
     * @param {BoundParams | null} [boundParams] - Optional parameters to pre-bind to the tools in the toolset.
     * @param {boolean} [strict=false] - If true, throws an error if any provided auth token or bound param is not used by at least one tool.
     * @returns {Promise<Array<ReturnType<typeof ToolboxTool>>>} A promise that resolves
     * to a list of ToolboxTool functions, ready for execution.
     * @throws {Error} If the manifest structure is invalid or if there's an error fetching data from the API.
     */
    loadToolset(name?: string, authTokenGetters?: AuthTokenGetters | null, boundParams?: BoundParams | null, strict?: boolean): Promise<Array<ReturnType<typeof ToolboxTool>>>;
}
export { ToolboxClient };
