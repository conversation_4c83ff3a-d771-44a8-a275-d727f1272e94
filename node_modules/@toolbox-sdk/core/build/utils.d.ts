export type BoundValue = unknown | (() => unknown) | (() => Promise<unknown>);
export type BoundParams = Record<string, BoundValue>;
type RequiredAuthnParams = Record<string, string[]>;
/**
 * Resolves a value that might be a literal, a function, or a promise-returning function.
 * @param {BoundValue} value The value to resolve.
 * @returns {Promise<unknown>} A promise that resolves to the final literal value.
 */
export declare function resolveValue(value: BoundValue): Promise<unknown>;
/**
 * Identifies authentication requirements.
 * @param reqAuthnParams - A mapping of parameter names to lists of required auth services.
 * @param reqAuthzTokens - A list of required authorization tokens.
 * @param authServiceNames - An iterable of available auth service names.
 * @returns A tuple containing remaining required params, remaining required tokens, and used services.
 */
export declare function identifyAuthRequirements(reqAuthnParams: RequiredAuthnParams, reqAuthzTokens: string[], authServiceNames: Iterable<string>): [RequiredAuthnParams, string[], Set<string>];
export {};
