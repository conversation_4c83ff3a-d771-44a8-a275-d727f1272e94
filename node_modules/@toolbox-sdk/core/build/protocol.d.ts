import { z, Zod<PERSON>awShape, ZodObject } from 'zod';
interface BaseParameter {
    name: string;
    description: string;
    authSources?: string[];
}
interface StringParameter extends BaseParameter {
    type: 'string';
}
interface IntegerParameter extends BaseParameter {
    type: 'integer';
}
interface FloatParameter extends BaseParameter {
    type: 'float';
}
interface BooleanParameter extends BaseParameter {
    type: 'boolean';
}
interface ArrayParameter extends BaseParameter {
    type: 'array';
    items: ParameterSchema;
}
export type ParameterSchema = StringParameter | IntegerParameter | FloatParameter | BooleanParameter | ArrayParameter;
export declare const ZodParameterSchema: z.ZodType<ParameterSchema>;
export declare const ZodToolSchema: z.ZodObject<{
    description: z.ZodString;
    parameters: z.ZodArray<z.ZodType<ParameterSchema, z.ZodTypeDef, ParameterSchema>, "many">;
    authRequired: z.Zod<PERSON>ptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    description: string;
    parameters: ParameterSchema[];
    authRequired?: string[] | undefined;
}, {
    description: string;
    parameters: ParameterSchema[];
    authRequired?: string[] | undefined;
}>;
export declare const ZodManifestSchema: z.ZodObject<{
    serverVersion: z.ZodString;
    tools: z.ZodRecord<z.ZodString, z.ZodObject<{
        description: z.ZodString;
        parameters: z.ZodArray<z.ZodType<ParameterSchema, z.ZodTypeDef, ParameterSchema>, "many">;
        authRequired: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        description: string;
        parameters: ParameterSchema[];
        authRequired?: string[] | undefined;
    }, {
        description: string;
        parameters: ParameterSchema[];
        authRequired?: string[] | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    serverVersion: string;
    tools: Record<string, {
        description: string;
        parameters: ParameterSchema[];
        authRequired?: string[] | undefined;
    }>;
}, {
    serverVersion: string;
    tools: Record<string, {
        description: string;
        parameters: ParameterSchema[];
        authRequired?: string[] | undefined;
    }>;
}>;
export type ZodManifest = z.infer<typeof ZodManifestSchema>;
/**
 * Creates a ZodObject schema from an array of ParameterSchema (TypeScript types).
 * This combined schema is used by ToolboxTool to validate its call arguments.
 * @param params Array of ParameterSchema objects.
 * @returns A ZodObject schema.
 */
export declare function createZodSchemaFromParams(params: ParameterSchema[]): ZodObject<ZodRawShape>;
export {};
