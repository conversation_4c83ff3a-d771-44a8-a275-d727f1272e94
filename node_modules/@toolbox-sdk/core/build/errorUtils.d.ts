/**
 * Logs a standardized error message to the console, differentiating between
 * Axios errors with response data, Axios errors without response data (e.g., network errors),
 * and other types of errors.
 *
 * @param {string} baseMessage - The base message to log, e.g., "Error fetching data from".
 * @param {unknown} error - The error object caught.
 */
export declare function logApiError(baseMessage: string, error: unknown): void;
