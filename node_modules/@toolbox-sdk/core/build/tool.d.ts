import { Zod<PERSON><PERSON>, ZodRawShape } from 'zod';
import { AxiosInstance } from 'axios';
import { BoundParams, BoundValue } from './utils.js';
import { ClientHeadersConfig } from './client.js';
export type AuthTokenGetter = () => string | Promise<string>;
export type AuthTokenGetters = Record<string, AuthTokenGetter>;
export type RequiredAuthnParams = Record<string, string[]>;
/**
 * Creates a callable tool function representing a specific tool on a remote
 * Toolbox server.
 *
 * @param {AxiosInstance} session - The Axios session for making HTTP requests.
 * @param {string} baseUrl - The base URL of the Toolbox Server API.
 * @param {string} name - The name of the remote tool.
 * @param {string} description - A description of the remote tool.
 * @param {ZodObject<any>} paramSchema - The Zod schema for validating the tool's parameters.
 * @param {AuthTokenGetters} [authTokenGetters] - Optional map of auth service names to token getters.
 * @param {RequiredAuthnParams} [requiredAuthnParams] - Optional map of auth params that still need satisfying.
 * @param {string[]} [requiredAuthzTokens] - Optional list of auth tokens that still need satisfying.
 * @param {BoundParams} [boundParams] - Optional parameters to pre-bind to the tool.
 * @param {ClientHeadersConfig} [clientHeaders] - Optional client-specific headers.
 * @returns {CallableTool & CallableToolProperties} An async function that, when
 * called, invokes the tool with the provided arguments.
 */
declare function ToolboxTool(session: AxiosInstance, baseUrl: string, name: string, description: string, paramSchema: ZodObject<ZodRawShape>, authTokenGetters?: AuthTokenGetters, requiredAuthnParams?: RequiredAuthnParams, requiredAuthzTokens?: string[], boundParams?: BoundParams, clientHeaders?: ClientHeadersConfig): {
    (callArguments?: Record<string, unknown>): Promise<string>;
    toolName: string;
    description: string;
    params: ZodObject<ZodRawShape, import("zod").UnknownKeysParam, import("zod").ZodTypeAny, {
        [x: string]: any;
    }, {
        [x: string]: any;
    }>;
    boundParams: BoundParams;
    authTokenGetters: AuthTokenGetters;
    requiredAuthnParams: RequiredAuthnParams;
    requiredAuthzTokens: string[];
    clientHeaders: ClientHeadersConfig;
    getName(): string;
    getDescription(): string;
    getParamSchema(): ZodObject<ZodRawShape, import("zod").UnknownKeysParam, import("zod").ZodTypeAny, {
        [x: string]: any;
    }, {
        [x: string]: any;
    }>;
    addAuthTokenGetters(newAuthTokenGetters: AuthTokenGetters): /*elided*/ any;
    addAuthTokenGetter(authSource: string, getIdToken: AuthTokenGetter): /*elided*/ any;
    bindParams(paramsToBind: BoundParams): /*elided*/ any;
    bindParam(paramName: string, paramValue: BoundValue): /*elided*/ any;
};
export { ToolboxTool };
