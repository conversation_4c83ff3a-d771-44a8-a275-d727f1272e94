{"name": "@toolbox-sdk/core", "version": "0.1.0", "type": "module", "description": "JavaScript Base SDK for interacting with the Toolbox service", "license": "Apache-2.0", "author": "Google LLC", "keywords": ["developers", "google", "toolbox", "sdk", "llm", "genai", "agents", "mcp"], "exports": {".": {"import": "./build/client.js", "require": "./build/cjs/client.js", "types": "./build/client.d.ts"}, "./auth": {"import": "./build/authMethods.js", "require": "./build/cjs/authMethods.js", "types": "./build/authMethods.d.ts"}}, "repository": {"type": "git", "url": "git+https://github.com/googleapis/mcp-toolbox-sdk-js.git"}, "homepage": "https://github.com/googleapis/mcp-toolbox-sdk-js/blob/main/packages/toolbox-core", "bugs": {"url": "https://github.com/googleapis/mcp-toolbox-sdk-js/issues"}, "engines": {"node": ">=20.0.0"}, "scripts": {"fix": "gts fix", "lint": "gts check", "compile": "npm run compile:esm && npm run compile:cjs", "compile:esm": "tsc -p tsconfig.esm.json", "compile:cjs": "tsc -p tsconfig.cjs.json", "prepare": "npm run compile", "test:unit": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config jest.config.json", "test:e2e": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config jest.e2e.config.json --runInBand", "coverage": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --config jest.config.json --coverage"}, "devDependencies": {"@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.16.0", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/tmp": "^0.2.6", "cross-env": "^7.0.3", "fs-extra": "^11.3.0", "gts": "^5.3.1", "jest": "^29.7.0", "tmp": "^0.2.3", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}, "dependencies": {"axios": "^1.9.0", "google-auth-library": "^9.15.1", "zod": "^3.24.4"}}