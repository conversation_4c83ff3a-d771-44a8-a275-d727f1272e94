# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

steps:
  - id: Install package
    name: 'node:${_VERSION}'
    entrypoint: /bin/bash
    dir: packages/toolbox-core
    args:
      - -c
      - npm ci
  - id: Run unit tests
    name: 'node:${_VERSION}'
    entrypoint: /bin/bash
    dir: packages/toolbox-core
    args:
      - '-c'
      - npm run test:unit
  - id: Run integration tests
    name: 'node:${_VERSION}'
    entrypoint: /bin/bash
    dir: packages/toolbox-core
    env:
      - TOOLBOX_URL=$_TOOLBOX_URL
      - TOOLBOX_VERSION=$_TOOLBOX_VERSION
      - GOOGLE_CLOUD_PROJECT=$PROJECT_ID
    args:
      - '-c'
      - npm run test:e2e
options:
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _VERSION: '22.16.0'
  _TOOLBOX_VERSION: '0.7.0'
