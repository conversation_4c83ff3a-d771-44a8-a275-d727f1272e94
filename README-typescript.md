# Hotel Agent - TypeScript Version with @toolbox-sdk/core

This is a TypeScript conversion of the Python hotel booking agent using the official `@toolbox-sdk/core` package along with Google Gemini AI.

## Prerequisites

1. **Node.js** (version 16 or higher)
2. **npm** or **yarn**
3. **Toolbox server** running on `http://127.0.0.1:5000`
4. **Google API Key** for Gemini

## Setup

1. **Install dependencies:**
   ```bash
   npm install @toolbox-sdk/core
   npm install
   ```

2. **Update your Google API Key:**
   Edit `hotel_agent.ts` and replace the API key:
   ```typescript
   const GOOGLE_API_KEY = 'your-actual-api-key-here';
   ```

3. **Start the Toolbox server:**
   Make sure the toolbox server is running:
   ```bash
   ./toolbox --tools-file tools.yaml
   ```

## Running the Application

### Development mode (with TypeScript):
```bash
npm run dev
```

### Production mode:
```bash
npm run build
npm start
```

## Key Features

### ✅ **Official SDK Integration**
- Uses `@toolbox-sdk/core` for proper toolbox integration
- Cleaner API calls compared to manual HTTP requests
- Better error handling and type safety

### 🔄 **Equivalent Functionality**
- ✅ Connects to toolbox service on `http://127.0.0.1:5000`
- ✅ Uses the same Google API key and Gemini model
- ✅ Runs the same hotel booking queries
- ✅ Handles tool calling for hotel operations

### 🏗️ **Architecture**
- **ToolboxClient**: Official SDK client from `@toolbox-sdk/core`
- **HotelAgent**: Main agent class using `@google/generative-ai`
- **InMemorySessionService**: Session management
- **InMemoryArtifactService**: Artifact storage
- **Runner**: Orchestrates conversation flow

## Tools Available

The agent uses these tools from the `my-toolset` toolset:
- `search-hotels-by-name`: Search for hotels by name
- `search-hotels-by-location`: Search for hotels by location  
- `book-hotel`: Book a hotel by ID
- `update-hotel`: Update hotel check-in/check-out dates
- `cancel-hotel`: Cancel a hotel booking

## Example Queries

The script runs through these queries automatically:
1. "Find hotels in Basel with Basel in it's name."
2. "Can you book the Hilton Basel for me?"
3. "Oh wait, this is too expensive. Please cancel it and book the Hyatt Regency instead."
4. "My check in dates would be from April 10, 2024 to April 19, 2024."

## Expected Output

```
🔍 Query: Find hotels in Basel with Basel in it's name.
🤖 Response: I found three hotels in Basel that match your request: Holiday Inn Basel (ID: 8), Hilton Basel (ID: 1), and Hyatt Regency Basel (ID: 3).

🔍 Query: Can you book the Hilton Basel for me?
🤖 Response: OK. I have booked Hilton Basel for you.

🔍 Query: Oh wait, this is too expensive. Please cancel it and book the Hyatt Regency instead.
🤖 Response: OK, I have cancelled the Hilton Basel booking. Now I will book the Hyatt Regency for you.

🔍 Query: My check in dates would be from April 10, 2024 to April 19, 2024.
🤖 Response: I have updated your check-in date to April 10, 2024 and check-out date to April 19, 2024 for the Hyatt Regency Basel (ID: 3).
```

## Troubleshooting

1. **Connection refused error**: Make sure the toolbox server is running on port 5000
2. **API key errors**: Verify your Google API key is valid and has access to Gemini
3. **Tool loading errors**: Ensure the toolbox service has the "my-toolset" configured
4. **SDK errors**: Make sure `@toolbox-sdk/core` is properly installed

## Advantages of Using @toolbox-sdk/core

- **Official Support**: Uses the official toolbox SDK
- **Better Type Safety**: Proper TypeScript definitions
- **Simplified API**: Cleaner method calls compared to raw HTTP
- **Error Handling**: Built-in error handling and retry logic
- **Future Compatibility**: Will receive updates and improvements

## Notes

- This implementation maintains the same database schema and toolbox configuration as the Python version
- The `@toolbox-sdk/core` package provides a more robust and maintainable solution
- Function calling mechanism integrates seamlessly with Gemini's tool calling features
