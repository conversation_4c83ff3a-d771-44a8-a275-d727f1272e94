import { GoogleGenerativeAI } from '@google/generative-ai';

// TODO(developer): replace this with your Google API key
const GOOGLE_API_KEY = 'AIzaSyCsrqqysLx18zQT_yNg38RFaPkv7q7XiHo';
const TOOLBOX_URL = 'http://127.0.0.1:5000';

interface SessionData {
  id: string;
  state: any;
  appName: string;
  userId: string;
}

interface MessageContent {
  role: 'user' | 'model';
  parts: Array<{ text?: string; functionCall?: any; functionResponse?: any }>;
}

class InMemorySessionService {
  private sessions: Map<string, SessionData> = new Map();

  async createSession(options: {
    state: any;
    appName: string;
    userId: string;
  }): Promise<SessionData> {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session: SessionData = {
      id: sessionId,
      state: options.state,
      appName: options.appName,
      userId: options.userId
    };
    
    this.sessions.set(sessionId, session);
    return session;
  }

  async getSession(sessionId: string): Promise<SessionData | undefined> {
    return this.sessions.get(sessionId);
  }
}

class InMemoryArtifactService {
  private artifacts: Map<string, any> = new Map();

  async store(key: string, artifact: any): Promise<void> {
    this.artifacts.set(key, artifact);
  }

  async retrieve(key: string): Promise<any> {
    return this.artifacts.get(key);
  }
}

class HotelAgent {
  private model: any;
  private name: string;
  private description: string;
  private instruction: string;
  private tools: any[];
  private toolboxClient: any;

  constructor(options: {
    model: string;
    name: string;
    description: string;
    instruction: string;
    tools: any[];
    toolboxClient: any;
  }) {
    const genAI = new GoogleGenerativeAI(GOOGLE_API_KEY);
    this.model = genAI.getGenerativeModel({ 
      model: options.model,
      tools: this.convertToolsToGeminiFormat(options.tools)
    });
    this.name = options.name;
    this.description = options.description;
    this.instruction = options.instruction;
    this.tools = options.tools;
    this.toolboxClient = options.toolboxClient;
  }

  private convertToolsToGeminiFormat(tools: any[]): any[] {
    return tools.map(tool => ({
      functionDeclarations: [{
        name: tool.getName(),
        description: tool.getDescription(),
        parameters: tool.getParams()
      }]
    }));
  }

  async processMessage(message: string): Promise<string[]> {
    const responses: string[] = [];
    
    try {
      const chat = this.model.startChat({
        history: [],
        generationConfig: {
          maxOutputTokens: 1000,
        },
      });

      const fullPrompt = `${this.instruction}\n\nUser: ${message}`;
      const result = await chat.sendMessage(fullPrompt);
      
      const response = await result.response;
      
      // Handle function calls
      if (response.functionCalls) {
        for (const functionCall of response.functionCalls) {
          try {
            // Find the tool by name and invoke it
            const tool = this.tools.find(t => t.getName() === functionCall.name);
            if (!tool) {
              throw new Error(`Tool ${functionCall.name} not found`);
            }
            const toolResult = await tool(functionCall.args);
            
            // Send function response back to model
            const functionResponse = {
              functionResponse: {
                name: functionCall.name,
                response: toolResult
              }
            };
            
            const followUpResult = await chat.sendMessage([functionResponse]);
            const followUpResponse = await followUpResult.response;
            
            if (followUpResponse.text()) {
              responses.push(followUpResponse.text());
            }
          } catch (toolError) {
            console.error('Tool execution error:', toolError);
            responses.push(`Error executing tool ${functionCall.name}: ${toolError}`);
          }
        }
      } else if (response.text()) {
        responses.push(response.text());
      }
      
    } catch (error) {
      console.error('Error processing message:', error);
      responses.push(`Error: ${error}`);
    }
    
    return responses;
  }
}

class Runner {
  private appName: string;
  private agent: HotelAgent;
  private artifactService: InMemoryArtifactService;
  private sessionService: InMemorySessionService;

  constructor(options: {
    appName: string;
    agent: HotelAgent;
    artifactService: InMemoryArtifactService;
    sessionService: InMemorySessionService;
  }) {
    this.appName = options.appName;
    this.agent = options.agent;
    this.artifactService = options.artifactService;
    this.sessionService = options.sessionService;
  }

  async run(options: {
    sessionId: string;
    userId: string;
    newMessage: MessageContent;
  }): Promise<MessageContent[]> {
    const session = await this.sessionService.getSession(options.sessionId);
    if (!session) {
      throw new Error(`Session ${options.sessionId} not found`);
    }

    const messageText = options.newMessage.parts
      .filter(part => part.text)
      .map(part => part.text)
      .join(' ');

    const responses = await this.agent.processMessage(messageText);
    
    return responses.map(text => ({
      role: 'model' as const,
      parts: [{ text }]
    }));
  }
}

async function main(): Promise<void> {
  // Initialize the Toolbox client using dynamic import
  // @ts-ignore
  const { ToolboxClient } = await import('@toolbox-sdk/core');
  const toolboxClient = new ToolboxClient(TOOLBOX_URL);

  const prompt = `
    You're a helpful hotel assistant. You handle hotel searching, booking and
    cancellations. When the user searches for a hotel, mention it's name, id,
    location and price tier. Always mention hotel ids while performing any
    searches. This is very important for any operations. For any bookings or
    cancellations, please provide the appropriate confirmation. Be sure to
    update checkin or checkout dates if mentioned by the user.
    Don't ask for confirmations from the user.
  `;

  try {
    // Load the toolset using the SDK
    const tools = await toolboxClient.loadToolset("my-toolset");
    
    const rootAgent = new HotelAgent({
      model: 'gemini-2.0-flash-001',
      name: 'hotel_agent',
      description: 'A helpful AI assistant.',
      instruction: prompt,
      tools: tools,
      toolboxClient: toolboxClient
    });

    const sessionService = new InMemorySessionService();
    const artifactsService = new InMemoryArtifactService();
    
    const session = await sessionService.createSession({
      state: {},
      appName: 'hotel_agent',
      userId: '123'
    });

    const runner = new Runner({
      appName: 'hotel_agent',
      agent: rootAgent,
      artifactService: artifactsService,
      sessionService: sessionService,
    });

    const queries = [
      "Find hotels in Basel with Basel in it's name.",
      "Can you book the Hilton Basel for me?",
      "Oh wait, this is too expensive. Please cancel it and book the Hyatt Regency instead.",
      "My check in dates would be from April 10, 2024 to April 19, 2024.",
    ];

    for (const query of queries) {
      console.log(`\n🔍 Query: ${query}`);
      
      const content: MessageContent = {
        role: 'user',
        parts: [{ text: query }]
      };
      
      const events = await runner.run({
        sessionId: session.id,
        userId: '123',
        newMessage: content
      });

      for (const event of events) {
        for (const part of event.parts) {
          if (part.text) {
            console.log(`🤖 Response: ${part.text}`);
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ Error in main:', error);
  }
}

// Run the main function
main().catch(console.error);
