{"name": "hotel-agent-ts", "version": "1.0.0", "description": "Hotel booking agent using Google Gemini and Toolbox SDK in TypeScript", "main": "dist/hotel_agent.js", "scripts": {"build": "tsc", "start": "node dist/hotel_agent.js", "dev": "ts-node hotel_agent.ts", "clean": "rm -rf dist"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@toolbox-sdk/core": "^1.0.0"}, "devDependencies": {"@types/node": "^22.5.4", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "keywords": ["ai", "agent", "hotel", "booking", "gemini", "typescript", "toolbox"], "author": "", "license": "MIT"}